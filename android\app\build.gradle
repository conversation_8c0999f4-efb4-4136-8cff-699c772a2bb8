plugins {
  id 'com.android.application'
  id 'org.jetbrains.kotlin.android'
  id 'com.facebook.react'
  id 'com.google.gms.google-services'
}


apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

project.ext.envConfigFiles = [
    debug: ".env",
    release: ".env",
    anothercustombuild: ".env",
]

def enableProguardInReleaseBuilds = false
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    namespace "com.panditnearme"
    compileSdk rootProject.ext.compileSdkVersion
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        applicationId "com.panditnearme"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 2
        versionName "1"
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                storeFile file(MYAPP_UPLOAD_STORE_FILE)
                storePassword MYAPP_UPLOAD_STORE_PASSWORD
                keyAlias MYAPP_UPLOAD_KEY_ALIAS
                keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}

dependencies {
    implementation("com.facebook.react:react-android")
    implementation platform('com.google.firebase:firebase-bom:32.8.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    implementation("com.facebook.react:hermes-android")
    implementation project(':react-native-firebase_messaging')
    }
