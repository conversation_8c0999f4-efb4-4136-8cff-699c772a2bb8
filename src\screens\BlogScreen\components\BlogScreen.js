import React, { useEffect, useState } from 'react';
import {
    View,
    StatusBar,
    FlatList,
    Image,
    Text,
    TouchableOpacity,
    ActivityIndicator,
    Dimensions,
} from 'react-native';
import { colors } from '../../../theme/colors';
import AppTitle from '../../../components/common/AppTitle';
import I18n from '../../../utils/language/i18nextConfig';
import styles from '../styles/BlogScreen.style';
import { GetServices } from '../../../services/commonApiMethod';
import Apis from '../../../services/apiList';
import HTML from 'react-native-render-html';
import AppRoutes from '../../../constants/AppRoutes';

const { width } = Dimensions.get('window');

const BlogScreen = ({ navigation, route }) => {
    var api = new Apis();
    const [blogs, setBlogs] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchBlogs();
    }, []);

    const fetchBlogs = async () => {
        try {
            console.log('Fetching blogs...');
            const payload = {
                url: `${api.posts}?_embed`,
            };
            console.log('API URL:', payload.url);
            const response = await GetServices(payload);
            console.log('Raw API Response:', response);

            if (response?.success && response?.data) {
                console.log('Setting blogs data:', response.data);
                setBlogs(response.data);
            } else {
                console.log('No data received from API or API call failed');
                console.log('Response:', response);
            }
        } catch (error) {
            console.error('Error fetching blogs:', error);
        } finally {
            setLoading(false);
        }
    };

    const renderBlogItem = ({ item }) => {
        console.log('Rendering blog item:', item);
        // Extract the first paragraph from content
        const content = item.content?.rendered || '';
        const excerpt = item.excerpt?.rendered || '';

        const imageUrl = item._embedded?.['wp:featuredmedia']?.[0]?.source_url;
        console.log('Image URL:', imageUrl);

        return (
            <View
                style={styles.blogItem}
                
            >
                {imageUrl ? (
                    <Image
                        source={{ uri: imageUrl }}
                        style={styles.blogImage}
                        resizeMode="cover"
                    />
                ) : (
                    <View style={[styles.blogImage, { backgroundColor: colors.grey }]} />
                )}
                <View style={styles.blogContent}>
                    <Text style={styles.blogTitle} numberOfLines={2}>
                        {item.title?.rendered?.replace(/&#8211;/g, '-')}
                    </Text>
                    <HTML
                        source={{ html: excerpt }}
                        contentWidth={width - 40}
                        tagsStyles={{
                            p: styles.blogExcerpt
                        }}
                    />
                    <View style={{flexDirection:'row',justifyContent:'space-between',flex:1}}>
                        <Text style={styles.blogDate}>
                            {new Date(item.date).toLocaleDateString()}
                        </Text>
                        <TouchableOpacity
                        onPress={() => navigation.navigate(AppRoutes.BLOG_DETAIL_SCREEN, { blog: item })}>
                            <Text style={styles.readmore}>{I18n.t('read_more')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <StatusBar backgroundColor={colors.background} barStyle="dark-content" />
            <View style={styles.apptitle}>
                <AppTitle
                    title={I18n.t('our_blogs')}
                    leftIconPress={() => navigation.goBack()}
                />
            </View>

            {loading ? (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                </View>
            ) : blogs.length > 0 ? (
                <FlatList
                    data={blogs}
                    renderItem={renderBlogItem}
                    keyExtractor={(item) => item.id.toString()}
                    contentContainerStyle={styles.blogList}
                    showsVerticalScrollIndicator={false}
                />
            ) : (
                <View style={styles.loadingContainer}>
                    <Text>No blogs found</Text>
                </View>
            )}
        </View>
    );
};

export default BlogScreen;
