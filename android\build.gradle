buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.9.24"
        playServicesLocationVersion = "21.0.1"
        
    }
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.google.com' }
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath 'com.google.gms:google-services:4.3.15'
    }
}

apply plugin: "com.facebook.react.rootproject"
