import translate from "translate";
import { get<PERSON><PERSON> } from "../helper/cookies";
import { ASYNC_KEY_CONSTANTS } from "../constants/AppConstants";
translate.engine = "google";

// export const translateText = async (text, targetLang) => {
//   try {
//     console.log("targetLang >> ",targetLang);
    
//     const res = await fetch(
//       `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`
//     );
//     const data = await res.json();
//     return data[0]?.[0]?.[0] || text;
//   } catch (err) {
//     console.error("❌ Translation failed for:", text, err);
//     return text; // fallback to original
//   }
// };

// translationService.js
export const translateText = async (text, targetLang = 'hi') => {
  try {
    const response = await fetch(
      `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`
    );

    const result = await response.json();

    // Extract translated text
    const translatedText = result?.[0]?.map((item) => item[0]).join('') || text;

    return translatedText;
  } catch (error) {
    console.error('Translation Error:', error);
    return text; // fallback to original
  }
};
